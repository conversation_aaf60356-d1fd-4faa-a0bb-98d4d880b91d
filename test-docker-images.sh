#!/bin/bash

# Docker镜像可用性测试脚本
# 测试部署所需的Docker镜像是否可以拉取

set -e

echo "========================================="
echo "    Docker镜像可用性测试"
echo "========================================="

# 定义候选镜像
declare -A images
images[openjdk]="openjdk:17-jre openjdk:17 openjdk:17-alpine eclipse-temurin:17-jre eclipse-temurin:17"
images[nginx]="nginx:alpine nginx:latest nginx:1.25-alpine"

# 测试镜像函数
test_image() {
    local image=$1
    echo "🔍 测试镜像: $image"
    
    if docker pull "$image" >/dev/null 2>&1; then
        echo "✅ $image 可用"
        return 0
    else
        echo "❌ $image 不可用"
        return 1
    fi
}

# 查找可用的OpenJDK镜像
echo "📦 查找可用的OpenJDK镜像..."
openjdk_image=""
for image in ${images[openjdk]}; do
    if test_image "$image"; then
        openjdk_image="$image"
        break
    fi
done

if [ -z "$openjdk_image" ]; then
    echo "❌ 未找到可用的OpenJDK镜像"
    exit 1
fi

# 查找可用的Nginx镜像
echo ""
echo "📦 查找可用的Nginx镜像..."
nginx_image=""
for image in ${images[nginx]}; do
    if test_image "$image"; then
        nginx_image="$image"
        break
    fi
done

if [ -z "$nginx_image" ]; then
    echo "❌ 未找到可用的Nginx镜像"
    exit 1
fi

echo ""
echo "🎉 镜像测试完成！"
echo ""
echo "可用镜像："
echo "  OpenJDK: $openjdk_image"
echo "  Nginx: $nginx_image"
echo ""

# 更新Dockerfile
echo "🔧 更新Dockerfile..."

# 更新后端Dockerfile
sed -i "s|FROM openjdk:.*|FROM $openjdk_image|" docker/backend.Dockerfile
echo "✅ 已更新 docker/backend.Dockerfile"

# 更新前端Dockerfile
sed -i "s|FROM nginx:.*|FROM $nginx_image|" docker/frontend.Dockerfile
echo "✅ 已更新 docker/frontend.Dockerfile"

echo ""
echo "✅ Dockerfile已自动更新为可用镜像"
echo "现在可以运行 ./start.sh 进行部署"
