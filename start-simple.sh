#!/bin/bash

# SDL Platform 简化启动脚本
# 专门处理网络问题和镜像拉取

set -e

echo "========================================="
echo "    SDL Platform 简化启动脚本"
echo "    (使用系统配置的Docker镜像源)"
echo "========================================="

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

echo "✅ Docker 环境检查通过"

# 显示当前Docker镜像源配置
echo "🔍 当前Docker镜像源配置:"
if [ -f /etc/docker/daemon.json ]; then
    cat /etc/docker/daemon.json | grep -A 5 "registry-mirrors" || echo "未找到镜像源配置"
else
    echo "未找到Docker配置文件"
fi

# 检查外部服务连接（简化版）
echo "🔍 检查外部服务连接..."
if command -v nc &> /dev/null; then
    if nc -z 118.31.37.253 3306 2>/dev/null; then
        echo "✅ MySQL连接正常"
    else
        echo "❌ MySQL连接失败"
        exit 1
    fi
    
    if nc -z 118.31.37.253 6379 2>/dev/null; then
        echo "✅ Redis连接正常"
    else
        echo "❌ Redis连接失败"
        exit 1
    fi
else
    echo "⚠️  nc命令不可用，跳过外部服务检查"
fi

# 创建环境文件
if [ ! -f .env ]; then
    echo "📝 创建环境配置文件..."
    cp .env.example .env
    echo "✅ 环境配置文件已创建"
fi

# 清理可能存在的容器
echo "🧹 清理现有容器..."
docker-compose down 2>/dev/null || true

# 尝试拉取镜像
echo "📥 预拉取Docker镜像..."
echo "正在拉取Maven镜像..."
if ! docker pull maven:3.9.6-openjdk-17; then
    echo "❌ Maven镜像拉取失败，请检查网络连接和镜像源配置"
    echo "💡 建议："
    echo "   1. 检查网络连接"
    echo "   2. 重启Docker服务: sudo systemctl restart docker"
    echo "   3. 检查镜像源配置是否正确"
    exit 1
fi

echo "正在拉取OpenJDK镜像..."
if ! docker pull openjdk:17-jre; then
    echo "❌ OpenJDK镜像拉取失败"
    exit 1
fi

echo "正在拉取Node.js镜像..."
if ! docker pull node:18-alpine; then
    echo "❌ Node.js镜像拉取失败"
    exit 1
fi

echo "正在拉取Nginx镜像..."
if ! docker pull nginx:1.25-alpine; then
    echo "❌ Nginx镜像拉取失败"
    exit 1
fi

echo "✅ 所有镜像拉取成功"

# 构建并启动服务
echo "🚀 构建并启动服务..."
if ! docker-compose up -d --build; then
    echo "❌ 服务启动失败，查看错误日志:"
    docker-compose logs
    exit 1
fi

echo "⏳ 等待服务启动..."
sleep 60

# 检查服务状态
echo "📊 服务状态:"
docker-compose ps

echo ""
echo "🎉 SDL Platform 部署完成!"
echo ""
echo "📱 访问地址:"
echo "   前端: http://localhost"
echo "   后端: http://localhost:8080"
echo "   API文档: http://localhost:8080/swagger-ui.html"
echo ""
echo "👤 默认账号:"
echo "   用户名: admin"
echo "   密码: admin123"
echo ""
echo "🛠️  管理命令:"
echo "   查看日志: docker-compose logs -f"
echo "   停止服务: docker-compose down"
echo "   重启服务: docker-compose restart"
echo ""
