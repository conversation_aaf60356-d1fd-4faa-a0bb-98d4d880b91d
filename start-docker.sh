#!/bin/bash

# SDL Platform 一键启动脚本
# 简化版启动脚本，用于快速部署

set -e

echo "========================================="
echo "    SDL Platform Docker 一键部署"
echo "    (使用外部MySQL和Redis服务)"
echo "========================================="

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

echo "✅ Docker 环境检查通过"

# 检查外部服务连接
echo "🔍 检查外部服务连接..."
if ! nc -z 118.31.37.253 3306 2>/dev/null; then
    echo "❌ 无法连接到MySQL服务 (118.31.37.253:3306)"
    echo "请确保MySQL服务正在运行且网络可达"
    exit 1
fi

if ! nc -z 118.31.37.253 6379 2>/dev/null; then
    echo "❌ 无法连接到Redis服务 (118.31.37.253:6379)"
    echo "请确保Redis服务正在运行且网络可达"
    exit 1
fi

echo "✅ 外部服务连接正常"

# 创建环境文件
if [ ! -f .env ]; then
    echo "📝 创建环境配置文件..."
    cp .env.example .env
    echo "✅ 环境配置文件已创建"
fi

# 停止可能存在的服务
echo "🛑 停止现有服务..."
docker-compose down 2>/dev/null || true

# 构建并启动服务
echo "🚀 构建并启动服务..."
docker-compose up -d --build

echo "⏳ 等待服务启动..."
sleep 60

# 检查服务状态
echo "📊 服务状态:"
docker-compose ps

echo ""
echo "🎉 SDL Platform 部署完成!"
echo ""
echo "📱 访问地址:"
echo "   前端: http://localhost"
echo "   后端: http://localhost:8080"
echo "   API文档: http://localhost:8080/swagger-ui.html"
echo ""
echo "👤 默认账号:"
echo "   用户名: admin"
echo "   密码: admin123"
echo ""
echo "📚 详细文档: ./docker/README.md"
echo "🛠️  管理命令:"
echo "   启动: docker-compose up -d"
echo "   停止: docker-compose down"
echo "   日志: docker-compose logs -f"
echo ""
