#!/bin/bash

# Docker镜像拉取测试脚本
# 用于测试所需的Docker镜像是否可以正常拉取

set -e

echo "========================================="
echo "    Docker镜像拉取测试"
echo "========================================="

# 定义所需的镜像
images=(
    "maven:3.9.6-openjdk-17"
    "openjdk:17-jre"
    "node:18-alpine"
    "nginx:1.25-alpine"
)

# 测试每个镜像
for image in "${images[@]}"; do
    echo "🔍 测试镜像: $image"
    
    if docker pull "$image"; then
        echo "✅ $image 拉取成功"
    else
        echo "❌ $image 拉取失败"
        echo "请检查网络连接和Docker镜像源配置"
        exit 1
    fi
    echo ""
done

echo "🎉 所有镜像拉取成功！"
echo "现在可以运行 ./start-simple.sh 启动服务"
