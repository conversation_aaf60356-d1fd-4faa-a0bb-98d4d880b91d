# version字段在新版本Docker Compose中已废弃，可以移除

services:

  # 后端服务
  sdl-backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: sdl-backend
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: public
      TZ: Asia/Shanghai
    ports:
      - "8080:8080"
    volumes:
      - backend_uploads:/app/uploadPath
      - backend_logs:/app/logs
    networks:
      - sdl-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

  # 前端服务
  sdl-frontend:
    build:
      context: .
      dockerfile: docker/frontend/Dockerfile
    container_name: sdl-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      - sdl-backend
    networks:
      - sdl-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

# 网络配置
networks:
  sdl-network:
    driver: bridge

# 数据卷配置
volumes:
  backend_uploads:
    driver: local
  backend_logs:
    driver: local
