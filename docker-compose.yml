services:
  # 后端服务
  sdl-backend:
    build:
      context: docker
      # 如果默认镜像不可用，可以改为 backend-simple.Dockerfile
      dockerfile: backend.Dockerfile
    container_name: sdl-backend
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: public
      TZ: Asia/Shanghai
    ports:
      - "8080:8080"
    volumes:
      - backend_uploads:/app/uploadPath
      - backend_logs:/app/logs
    networks:
      - sdl-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # 前端服务
  sdl-frontend:
    build:
      context: docker
      # 如果默认镜像不可用，可以改为 frontend-simple.Dockerfile
      dockerfile: frontend.Dockerfile
    container_name: sdl-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      sdl-backend:
        condition: service_healthy
    networks:
      - sdl-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3

# 网络配置
networks:
  sdl-network:
    driver: bridge

# 数据卷配置
volumes:
  backend_uploads:
    driver: local
  backend_logs:
    driver: local