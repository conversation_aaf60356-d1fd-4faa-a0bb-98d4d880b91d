version: '3.8'

services:
  # MySQL数据库
  sdl-mysql:
    image: mysql:8.0
    container_name: sdl-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: sdl
      MYSQL_USER: sdl
      MYSQL_PASSWORD: sdl123456
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/01-init.sql
      - ./sql:/docker-entrypoint-initdb.d/sql
      - mysql_logs:/var/log/mysql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - sdl-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p123456"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Redis缓存
  sdl-redis:
    image: redis:7.2-alpine
    container_name: sdl-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/etc/redis/redis.conf
    command: redis-server /etc/redis/redis.conf
    networks:
      - sdl-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 后端服务
  sdl-backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: sdl-backend
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: docker
      MYSQL_USER: root
      MYSQL_PASSWORD: 123456
      REDIS_PASSWORD: ""
      JWT_SECRET: abcdefghijklmnopqrstuvwxyz
      TZ: Asia/Shanghai
    ports:
      - "8080:8080"
    volumes:
      - backend_uploads:/app/uploadPath
      - backend_logs:/app/logs
    depends_on:
      sdl-mysql:
        condition: service_healthy
      sdl-redis:
        condition: service_healthy
    networks:
      - sdl-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

  # 前端服务
  sdl-frontend:
    build:
      context: .
      dockerfile: docker/frontend/Dockerfile
    container_name: sdl-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      sdl-backend:
        condition: service_healthy
    networks:
      - sdl-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

# 网络配置
networks:
  sdl-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  mysql_data:
    driver: local
  mysql_logs:
    driver: local
  redis_data:
    driver: local
  backend_uploads:
    driver: local
  backend_logs:
    driver: local
