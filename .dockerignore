# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
CLAUDE.md
docs/
*.md

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Maven
target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/

# Gradle
.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# IntelliJ IDEA
.idea
*.iws
*.iml
*.ipr

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# VS Code
.vscode/

# Docker
docker-compose.override.yml
docker-compose.*.yml
!docker-compose.yml

# Removed Docker config files (using external services)
docker/mysql/
docker/redis/
docker/backend/application-docker.yml

# Backup files
*.backup
*.bak
*.tmp

# Windows batch files
*.bat

# Shell scripts (except Docker scripts)
*.sh
!docker/scripts/*.sh
!start-docker.sh
