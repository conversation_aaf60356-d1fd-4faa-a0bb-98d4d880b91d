services:
  # 后端服务 - 使用简化镜像
  sdl-backend:
    build:
      context: docker
      dockerfile: backend-simple.Dockerfile
    container_name: sdl-backend
    restart: unless-stopped
    environment:
      TZ: Asia/Shanghai
    ports:
      - "8080:8080"
    volumes:
      - backend_uploads:/app/uploadPath
      - backend_logs:/app/logs

  # 前端服务 - 使用简化镜像
  sdl-frontend:
    build:
      context: docker
      dockerfile: frontend-simple.Dockerfile
    container_name: sdl-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      - sdl-backend

# 数据卷配置
volumes:
  backend_uploads:
    driver: local
  backend_logs:
    driver: local
