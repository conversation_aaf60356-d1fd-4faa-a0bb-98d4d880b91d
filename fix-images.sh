#!/bin/bash

# Docker镜像问题快速修复脚本

set -e

echo "========================================="
echo "    Docker镜像问题快速修复"
echo "========================================="

echo "🔍 检测可用的Docker镜像..."

# 检测Java镜像
java_image=""
if docker pull eclipse-temurin:17-jre >/dev/null 2>&1; then
    java_image="eclipse-temurin:17-jre"
    echo "✅ 使用 eclipse-temurin:17-jre"
elif docker pull openjdk:17-jre >/dev/null 2>&1; then
    java_image="openjdk:17-jre"
    echo "✅ 使用 openjdk:17-jre"
elif docker pull openjdk:17 >/dev/null 2>&1; then
    java_image="openjdk:17"
    echo "✅ 使用 openjdk:17"
else
    echo "❌ 无法找到可用的Java镜像"
    exit 1
fi

# 检测Nginx镜像
nginx_image=""
if docker pull nginx:alpine >/dev/null 2>&1; then
    nginx_image="nginx:alpine"
    echo "✅ 使用 nginx:alpine"
elif docker pull nginx >/dev/null 2>&1; then
    nginx_image="nginx"
    echo "✅ 使用 nginx"
else
    echo "❌ 无法找到可用的Nginx镜像"
    exit 1
fi

echo ""
echo "🔧 更新Dockerfile..."

# 更新后端Dockerfile
if [ -f "docker/backend.Dockerfile" ]; then
    sed -i "s|FROM.*temurin.*|FROM $java_image|" docker/backend.Dockerfile
    sed -i "s|FROM.*openjdk.*|FROM $java_image|" docker/backend.Dockerfile
    echo "✅ 已更新 docker/backend.Dockerfile"
fi

if [ -f "docker/backend-simple.Dockerfile" ]; then
    sed -i "s|FROM.*openjdk.*|FROM $java_image|" docker/backend-simple.Dockerfile
    echo "✅ 已更新 docker/backend-simple.Dockerfile"
fi

# 更新前端Dockerfile
if [ -f "docker/frontend.Dockerfile" ]; then
    sed -i "s|FROM.*nginx.*|FROM $nginx_image|" docker/frontend.Dockerfile
    echo "✅ 已更新 docker/frontend.Dockerfile"
fi

if [ -f "docker/frontend-simple.Dockerfile" ]; then
    sed -i "s|FROM.*nginx.*|FROM $nginx_image|" docker/frontend-simple.Dockerfile
    echo "✅ 已更新 docker/frontend-simple.Dockerfile"
fi

echo ""
echo "🎉 镜像修复完成！"
echo ""
echo "使用的镜像："
echo "  Java: $java_image"
echo "  Nginx: $nginx_image"
echo ""
echo "现在可以运行 ./start.sh 进行部署"
