# 多阶段构建 - 构建阶段
# 使用官方Maven镜像，通过系统配置的镜像源拉取
FROM maven:3.9.6-openjdk-17 AS builder

# 设置工作目录
WORKDIR /app

# 配置Maven使用阿里云镜像源
RUN mkdir -p /root/.m2 && \
    echo '<?xml version="1.0" encoding="UTF-8"?>' > /root/.m2/settings.xml && \
    echo '<settings>' >> /root/.m2/settings.xml && \
    echo '  <mirrors>' >> /root/.m2/settings.xml && \
    echo '    <mirror>' >> /root/.m2/settings.xml && \
    echo '      <id>aliyun</id>' >> /root/.m2/settings.xml && \
    echo '      <name><PERSON>yun Maven</name>' >> /root/.m2/settings.xml && \
    echo '      <url>https://maven.aliyun.com/repository/public</url>' >> /root/.m2/settings.xml && \
    echo '      <mirrorOf>central</mirrorOf>' >> /root/.m2/settings.xml && \
    echo '    </mirror>' >> /root/.m2/settings.xml && \
    echo '  </mirrors>' >> /root/.m2/settings.xml && \
    echo '</settings>' >> /root/.m2/settings.xml

# 复制pom文件
COPY pom.xml .
COPY sdl-platform-admin/pom.xml sdl-platform-admin/
COPY sdl-platform-framework/pom.xml sdl-platform-framework/
COPY sdl-platform-system/pom.xml sdl-platform-system/
COPY sdl-platform-quartz/pom.xml sdl-platform-quartz/
COPY sdl-platform-generator/pom.xml sdl-platform-generator/
COPY sdl-platform-common/pom.xml sdl-platform-common/
COPY sdl-platform-business/pom.xml sdl-platform-business/

# 下载依赖（利用Docker缓存层）
RUN mvn dependency:go-offline -B

# 复制源代码
COPY sdl-platform-admin/src sdl-platform-admin/src
COPY sdl-platform-framework/src sdl-platform-framework/src
COPY sdl-platform-system/src sdl-platform-system/src
COPY sdl-platform-quartz/src sdl-platform-quartz/src
COPY sdl-platform-generator/src sdl-platform-generator/src
COPY sdl-platform-common/src sdl-platform-common/src
COPY sdl-platform-business/src sdl-platform-business/src

# 构建应用
RUN mvn clean package -DskipTests -B

# 运行阶段
# 使用官方OpenJDK镜像，通过系统配置的镜像源拉取
FROM openjdk:17-jre

# 安装必要的工具
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 设置工作目录
WORKDIR /app

# 创建必要的目录
RUN mkdir -p /app/logs /app/uploadPath && \
    chown -R appuser:appuser /app

# 从构建阶段复制jar文件
COPY --from=builder /app/sdl-platform-admin/target/sdl-platform-admin.jar app.jar

# 不需要复制额外的配置文件，直接使用public配置

# 更改文件所有者
RUN chown -R appuser:appuser /app

# 切换到应用用户
USER appuser

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-Dspring.profiles.active=public", "-jar", "app.jar"]
