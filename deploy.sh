#!/bin/bash

# SDL Platform Docker 部署脚本
# 部署已构建的应用到Docker容器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "========================================="
echo "    SDL Platform Docker 部署"
echo "========================================="

# 检查构建产物
check_build_artifacts() {
    log_info "检查构建产物..."
    
    # 检查后端jar文件
    if [ ! -f "docker/deploy/backend/sdl-platform-admin.jar" ]; then
        log_error "后端jar文件不存在，请先运行 ./build.sh"
        exit 1
    fi
    log_success "后端jar文件检查通过"
    
    # 检查前端构建产物
    if [ ! -d "docker/deploy/frontend" ] || [ ! -f "docker/deploy/frontend/index.html" ]; then
        log_error "前端构建产物不存在，请先运行 ./build.sh"
        exit 1
    fi
    log_success "前端构建产物检查通过"
}

# 检查外部服务
check_external_services() {
    log_info "检查外部服务连接..."
    
    # 检查MySQL连接
    if command -v nc &> /dev/null; then
        if nc -z 118.31.37.253 3306 2>/dev/null; then
            log_success "MySQL连接正常"
        else
            log_error "无法连接到MySQL服务 (118.31.37.253:3306)"
            exit 1
        fi
        
        if nc -z 118.31.37.253 6379 2>/dev/null; then
            log_success "Redis连接正常"
        else
            log_error "无法连接到Redis服务 (118.31.37.253:6379)"
            exit 1
        fi
    else
        log_warning "nc命令不可用，跳过外部服务检查"
    fi
}

# 检查Docker环境
check_docker() {
    log_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    log_success "Docker环境检查通过"
}

# 停止现有服务
stop_existing_services() {
    log_info "停止现有服务..."
    docker-compose down 2>/dev/null || true
    log_success "现有服务已停止"
}

# 构建并启动Docker服务
deploy_services() {
    log_info "构建并启动Docker服务..."
    
    # 构建镜像
    log_info "构建Docker镜像..."
    if docker-compose build; then
        log_success "Docker镜像构建成功"
    else
        log_error "Docker镜像构建失败"
        exit 1
    fi
    
    # 启动服务
    log_info "启动Docker服务..."
    if docker-compose up -d; then
        log_success "Docker服务启动成功"
    else
        log_error "Docker服务启动失败"
        exit 1
    fi
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务启动..."
    
    # 等待后端服务
    log_info "等待后端服务启动..."
    timeout=120
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:8080/actuator/health >/dev/null 2>&1; then
            log_success "后端服务已就绪"
            break
        fi
        sleep 5
        timeout=$((timeout-5))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "后端服务启动超时"
        log_info "查看后端服务日志:"
        docker-compose logs --tail=20 sdl-backend
        exit 1
    fi
    
    # 等待前端服务
    log_info "等待前端服务启动..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost/ >/dev/null 2>&1; then
            log_success "前端服务已就绪"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "前端服务启动超时"
        log_info "查看前端服务日志:"
        docker-compose logs --tail=20 sdl-frontend
        exit 1
    fi
}

# 显示部署结果
show_result() {
    log_info "部署状态:"
    docker-compose ps
    
    echo ""
    log_success "SDL Platform 部署完成！"
    echo ""
    echo "📱 访问地址:"
    echo "   前端: http://localhost"
    echo "   后端API: http://localhost:8080"
    echo "   API文档: http://localhost:8080/swagger-ui.html"
    echo "   Druid监控: http://localhost:8080/druid (用户名: ruoyi, 密码: 123456)"
    echo ""
    echo "👤 默认账号:"
    echo "   用户名: admin"
    echo "   密码: admin123"
    echo ""
    echo "🛠️  管理命令:"
    echo "   查看日志: docker-compose logs -f"
    echo "   停止服务: docker-compose down"
    echo "   重启服务: docker-compose restart"
    echo ""
}

# 主函数
main() {
    check_build_artifacts
    check_docker
    check_external_services
    stop_existing_services
    deploy_services
    wait_for_services
    show_result
}

# 执行主函数
main "$@"
