# SDL Platform Docker 故障排除指南

## 常见问题及解决方案

### 1. Docker镜像拉取失败

#### 问题现象
```
failed to solve: openjdk:17-jre-slim: failed to resolve source metadata
```

#### 解决方案

**方案1: 检查镜像源配置**
```bash
# 查看当前Docker镜像源配置
cat /etc/docker/daemon.json

# 确保配置正确
{
    "registry-mirrors": [
        "https://docker.mirrors.aster.edu.pl",
        "https://docker.mirrors.imoyuapp.win"
    ]
}

# 重启Docker服务
sudo systemctl restart docker
```

**方案2: 手动拉取镜像**
```bash
# 使用简化启动脚本，会预先拉取镜像
./start-simple.sh

# 或手动拉取所需镜像
docker pull maven:3.9.6-openjdk-17
docker pull openjdk:17-jre
docker pull node:18-alpine
docker pull nginx:1.25-alpine
```

**方案3: 测试镜像源连通性**
```bash
# 测试镜像源连接
curl -I https://docker.mirrors.aster.edu.pl/v2/
curl -I https://docker.mirrors.imoyuapp.win/v2/

# 如果都不可用，尝试其他镜像源
```

### 2. 外部服务连接失败

#### 问题现象
```
无法连接到MySQL服务 (*************:3306)
无法连接到Redis服务 (*************:6379)
```

#### 解决方案

**检查网络连通性**
```bash
# 检查基本连通性
ping *************

# 检查端口连通性
telnet ************* 3306
telnet ************* 6379

# 或使用nc命令
nc -z ************* 3306
nc -z ************* 6379
```

**检查防火墙设置**
```bash
# 检查本地防火墙
sudo ufw status
sudo iptables -L

# 确保允许出站连接到3306和6379端口
```

### 3. 服务启动超时

#### 问题现象
```
后端服务启动超时，请检查日志
```

#### 解决方案

**查看详细日志**
```bash
# 查看后端服务日志
docker-compose logs -f sdl-backend

# 查看所有服务日志
docker-compose logs -f
```

**常见启动问题**
1. **数据库连接失败**: 检查application-public.yml中的数据库配置
2. **内存不足**: 增加Docker内存限制或系统内存
3. **端口冲突**: 检查8080端口是否被占用

### 4. Maven依赖下载失败

#### 问题现象
```
Could not transfer artifact from/to central
```

#### 解决方案

**检查Maven镜像源**
```bash
# 查看Maven配置（在容器内）
docker-compose exec sdl-backend cat /root/.m2/settings.xml
```

**手动配置Maven镜像源**
```bash
# 如果需要，可以修改Dockerfile中的Maven配置
# 当前使用阿里云镜像源: https://maven.aliyun.com/repository/public
```

### 5. NPM依赖安装失败

#### 问题现象
```
npm ERR! network request failed
```

#### 解决方案

**检查NPM镜像源**
```bash
# 前端Dockerfile中已配置淘宝镜像源
# https://registry.npmmirror.com
```

### 6. 版本警告

#### 问题现象
```
WARN[0000] /opt/sdl-platform/docker-compose.yml: `version` is obsolete
```

#### 解决方案
这是一个警告，不影响功能。新版本Docker Compose不再需要version字段，已经移除。

### 7. 容器健康检查失败

#### 问题现象
```
Health check failed
```

#### 解决方案

**检查服务状态**
```bash
# 查看容器状态
docker-compose ps

# 手动测试健康检查
curl http://localhost:8080/actuator/health
curl http://localhost/health
```

## 调试命令

### 基本调试
```bash
# 查看容器状态
docker-compose ps

# 查看容器日志
docker-compose logs -f [service_name]

# 进入容器
docker-compose exec sdl-backend bash
docker-compose exec sdl-frontend sh

# 查看容器资源使用
docker stats
```

### 网络调试
```bash
# 查看Docker网络
docker network ls
docker network inspect sdl-platform_sdl-network

# 测试容器间网络连接
docker-compose exec sdl-frontend ping sdl-backend
```

### 数据卷调试
```bash
# 查看数据卷
docker volume ls
docker volume inspect sdl-platform_backend_uploads

# 查看挂载点
docker-compose exec sdl-backend ls -la /app/uploadPath
```

## 完全重置

如果遇到无法解决的问题，可以完全重置：

```bash
# 停止并删除所有容器
docker-compose down

# 删除相关镜像
docker rmi $(docker images | grep sdl | awk '{print $3}')

# 清理系统
docker system prune -f

# 重新开始
./start-simple.sh
```

## 获取帮助

1. **查看日志**: 首先查看详细的容器日志
2. **检查配置**: 确认所有配置文件正确
3. **网络测试**: 使用提供的网络检查脚本
4. **逐步排查**: 按照本指南逐步排查问题

如果问题仍然存在，请提供：
- 完整的错误日志
- 系统环境信息
- Docker版本信息
- 网络配置信息
