# SDL Platform 前端部署镜像
# 仅用于部署已构建的静态文件

FROM nginx:alpine

# 安装curl用于健康检查
RUN apk add --no-cache curl

# 复制已构建的前端文件
COPY deploy/frontend/ /usr/share/nginx/html/

# 复制nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 创建nginx用户目录
RUN chown -R nginx:nginx /usr/share/nginx/html

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
