# SDL Platform 后端部署镜像
# 仅用于运行已编译的jar文件

FROM openjdk:17-jre-slim

# 安装必要工具
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 设置工作目录
WORKDIR /app

# 创建必要目录
RUN mkdir -p /app/logs /app/uploadPath && \
    chown -R appuser:appuser /app

# 复制已编译的jar文件
COPY deploy/backend/sdl-platform-admin.jar app.jar

# 更改文件所有者
RUN chown -R appuser:appuser /app

# 切换到应用用户
USER appuser

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-Dspring.profiles.active=public", "-jar", "app.jar"]
