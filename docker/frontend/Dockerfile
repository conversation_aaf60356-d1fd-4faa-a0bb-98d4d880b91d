# 多阶段构建 - 构建阶段
# 使用阿里云镜像源的Node.js镜像
FROM registry.cn-hangzhou.aliyuncs.com/library/node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 设置npm镜像源
RUN npm config set registry https://registry.npmmirror.com

# 复制package文件
COPY sdl-platform-vue3/package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY sdl-platform-vue3/ .

# 构建应用
RUN npm run build:prod

# 运行阶段
# 使用阿里云镜像源的Nginx镜像
FROM registry.cn-hangzhou.aliyuncs.com/library/nginx:1.25-alpine

# 安装必要的工具
RUN apk add --no-cache curl

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY docker/frontend/nginx.conf /etc/nginx/nginx.conf
COPY docker/frontend/default.conf /etc/nginx/conf.d/default.conf

# 创建nginx用户目录
RUN mkdir -p /var/cache/nginx/client_temp && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /usr/share/nginx/html

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
